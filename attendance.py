from tkinter import *
from tkinter import ttk
from tkinter import messagebox as msgbox
from PIL import Image, ImageTk
from tkcalendar import DateEntry
import mysql.connector
import cv2
class Attendance:
    def __init__(self, root):
        self.root = root
        self.root.geometry("1530x790+0+0")
        self.root.title("Attendance")  # Change the title to "Attendance"
               
        # first image
        
        img = Image.open(r"Project_images\smart-attendance.jpg")
        img = img.resize((800, 200), Image.LANCZOS)
        self.photo = ImageTk.PhotoImage(img)
        f_lbl = Label(self.root, image=self.photo)        
        f_lbl.place(x=0, y=0, width=765, height=200) 
        
        # Second image
        
        img1 = Image.open(r"Project_images\iStock-182059956_18390_t12.jpg")
        img1 = img1.resize((800, 200), Image.LANCZOS)
        self.photo1 = ImageTk.PhotoImage(img1)        
        f_lbl = Label(self.root, image=self.photo1)        
        f_lbl.place(x=765, y=0, width=765, height=200) 
        
        # bg image
        img2 = Image.open(r"Project_images\bg.jpg")
        img2 = img2.resize((1530, 710), Image.LANCZOS)
        self.photo2 = ImageTk.PhotoImage(img2)
        bg_img = Label(self.root, image=self.photo2)
        bg_img.place(x=0, y=201, width=1530, height=710)
        
        title_lbl = Label(self.root, text="ATTENDANCE", font=("arial", 25, "bold"), bg="white", fg="red")
        title_lbl.place(x=0, y=0, width=1530, height=45) 

if __name__ == "__main__":
    root=Tk()
    obj=Attendance(root)
    root.mainloop()