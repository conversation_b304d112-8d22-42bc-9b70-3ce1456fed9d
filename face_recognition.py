from tkinter import *
from tkinter import messagebox as msgbox # Import the messagebox module from tkinter
from PIL import Image, ImageTk
from datetime import datetime
import cv2
import mysql.connector
class Face_Recognition:
    def __init__(self, root):
        self.root = root
        self.root.geometry("1530x790+0+0")
        self.root.title("Face Recognition")

        # Title label
        title_lbl = Label(self.root, text="FACE RECOGNITION", font=("arial", 25, "bold"), bg="white", fg="red")
        title_lbl.place(x=0, y=0, width=1530, height=47)

        # First image
        img_left = Image.open(r"Project_images\face_detector1.jpg")
        img_left = img_left.resize((650, 700), Image.LANCZOS)
        self.photo_left = ImageTk.PhotoImage(img_left)
        f_lbl_left = Label(self.root, image=self.photo_left)
        f_lbl_left.place(x=0, y=55, width=650, height=700)

        # Title for first image (positioned over the image)
        img1_title = Label(self.root, text="FACE DETECTION", font=("arial", 18, "bold"),
                          bg="black", fg="white", relief="raised", bd=2)
        img1_title.place(x=200, y=80, width=250, height=40)

        # Second image
        img_right = Image.open(r"Project_images\facial_recognition_system_identification_digital_id_security_scanning_thinkstock_858236252_3x3-100740902-large.jpg")
        img_right = img_right.resize((950, 700), Image.LANCZOS)
        self.photo_right = ImageTk.PhotoImage(img_right)
        f_lbl_right = Label(self.root, image=self.photo_right)
        f_lbl_right.place(x=650, y=55, width=950, height=700)

        # Title for second image (positioned over the image)
        img2_title = Label(self.root, text="ATTENDANCE SYSTEM", font=("arial", 18, "bold"),
                          bg="black", fg="white", relief="raised", bd=2)
        img2_title.place(x=1000, y=80, width=300, height=40)

        # Face Recognition Button
        Button(self.root, text="FACE RECOGNITION", command=self.face_recog, cursor="hand2",
               font=("times new roman", 15, "bold"), bg="darkgreen", fg="white").place(x=1012, y=680, width=220, height=41)
#===============Attendance=================
    def mark_attendance(self, n, r, d, id):
        with open("attendance.csv", "a+", newline="\n") as f:
            myDataList = f.readlines()
            name_list = []
            for line in myDataList:
                entry = line.split((", "))
                name_list.append(entry[0])
            if (n not in name_list) and (r not in name_list) and (d not in name_list) and (id not in name_list):
                now = datetime.now()
                d1 = now.strftime("%d/%m/%Y")
                dtString = now.strftime("%H:%M:%S")
                f.writelines(f"\n{n},{r},{d},{id},{dtString},{d1},Present")

#==========Face Recognition Function==========
    def face_recog(self):
        # Cache for student information to avoid repeated database queries
        student_cache = {}
        # Track attendance marked in this session to prevent duplicates
        attendance_marked = {}

        def draw_boundary(img, classifier, scaleFactor, minNeighbors, clf):
            nonlocal student_cache, attendance_marked

            gray_image = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
            features = classifier.detectMultiScale(gray_image, scaleFactor, minNeighbors)

            for (x, y, w, h) in features:
                id, predict = clf.predict(gray_image[y:y+h, x:x+w])
                confidence = int(100 * (1 - predict / 300))



                # Initialize default values
                n = "Unknown"
                r = "Unknown"
                d = "Unknown"

                # Only query database if confidence is good and not already cached
                if confidence > 77:  # Changed from 50 to 77 for better detection
                    if id not in student_cache:
                        # Query database only once per student ID
                        try:
                            conn = mysql.connector.connect(host="localhost", username="root", password="Admin@1402", database="face_recognizer")
                            my_cursor = conn.cursor()

                            my_cursor.execute("SELECT Name, Roll, Dep FROM student WHERE student_id = %s", (id,))
                            result = my_cursor.fetchone()

                            if result:
                                student_cache[id] = {
                                    'name': result[0] if result[0] else "Unknown",
                                    'roll': result[1] if result[1] else "Unknown",
                                    'dept': result[2] if result[2] else "Unknown"
                                }
                            else:
                                student_cache[id] = {'name': "Unknown", 'roll': "Unknown", 'dept': "Unknown"}

                            conn.close()

                        except Exception as e:
                            print(f"Database error: {e}")
                            student_cache[id] = {'name': "DB Error", 'roll': "DB Error", 'dept': "DB Error"}

                    # Get student info from cache
                    if id in student_cache:
                        n = student_cache[id]['name']
                        r = student_cache[id]['roll']
                        d = student_cache[id]['dept']

                # Display information and mark attendance with proper duplicate prevention
                if confidence > 77 and n != "Unknown":  # Changed from 75 to 77 for better detection
                    # Known student with good confidence - green box
                    cv2.rectangle(img, (x, y), (x+w, y+h), (0, 255, 0), 3)
                    cv2.putText(img, f"Name: {n}", (x, y-75), cv2.FONT_HERSHEY_COMPLEX, 0.7, (0, 255, 0), 2)
                    cv2.putText(img, f"Roll: {r}", (x, y-55), cv2.FONT_HERSHEY_COMPLEX, 0.7, (0, 255, 0), 2)
                    cv2.putText(img, f"Dept: {d}", (x, y-35), cv2.FONT_HERSHEY_COMPLEX, 0.7, (0, 255, 0), 2)
                    cv2.putText(img, f"ID: {id}", (x, y-15), cv2.FONT_HERSHEY_COMPLEX, 0.7, (0, 255, 0), 2)
                    cv2.putText(img, f"{confidence}%", (x, y+h+20), cv2.FONT_HERSHEY_COMPLEX, 0.6, (0, 255, 0), 2)

                    # Mark attendance only once per session for each student
                    if id not in attendance_marked:
                        self.mark_attendance(n, r, d, id)
                        attendance_marked[id] = True
                        print(f"✓ Attendance marked for {n} (ID: {id})")

                else:
                    # Unknown face - red box for all unrecognized faces
                    cv2.rectangle(img, (x, y), (x+w, y+h), (0, 0, 255), 3)
                    cv2.putText(img, f"Unknown ({confidence}%)", (x, y-10), cv2.FONT_HERSHEY_COMPLEX, 0.6, (0, 0, 255), 2)
        


        try:
            faceCascade = cv2.CascadeClassifier("haarcascade_frontalface_default.xml")
            if faceCascade.empty():
                msgbox.showerror("Error", "Face cascade file not found!", parent=self.root)
                return

            clf = cv2.face.LBPHFaceRecognizer_create()
            clf.read("classifier.xml")

            # Use camera 1 (known working) with fallback to camera 0
            video_capture = cv2.VideoCapture(1)
            if not video_capture.isOpened():
                video_capture = cv2.VideoCapture(0)

            if not video_capture.isOpened():
                msgbox.showerror("Error", "Cannot open camera!", parent=self.root)
                return

            msgbox.showinfo("Info", "Face recognition started. Press ESC to stop.", parent=self.root)

            while True:
                ret, img = video_capture.read()
                if not ret:
                    print("Failed to read from camera")
                    break

                draw_boundary(img, faceCascade, 1.1, 5, clf)
                cv2.imshow("Welcome To Face Recognition", img)

                # Press ESC to exit (key code 27)
                if cv2.waitKey(1) & 0xFF == 27:
                    break

            # Clean up (moved outside the while loop)
            video_capture.release()
            cv2.destroyAllWindows()

        except Exception as e:
            msgbox.showerror("Error", f"Face recognition failed: {str(e)}", parent=self.root)
       
if __name__ == "__main__":
    root=Tk()
    obj=Face_Recognition(root)
    root.mainloop()          