from tkinter import *
from tkinter import messagebox as msgbox
from PIL import Image, ImageTk
from datetime import datetime
import cv2
import mysql.connector

class Face_Recognition:
    def __init__(self, root):
        self.root = root
        self.root.geometry("1530x790+0+0")
        self.root.title("Face Recognition")

        title_lbl = Label(self.root, text="FACE RECOGNITION", font=("arial", 25, "bold"), bg="white", fg="red")
        title_lbl.place(x=0, y=0, width=1530, height=47)

        img_left = Image.open(r"Project_images\face_detector1.jpg")
        img_left = img_left.resize((650, 700), Image.LANCZOS)
        self.photo_left = ImageTk.PhotoImage(img_left)
        f_lbl_left = Label(self.root, image=self.photo_left)
        f_lbl_left.place(x=0, y=55, width=650, height=700)

        img_right = Image.open(r"Project_images\facial_recognition_system_identification_digital_id_security_scanning_thinkstock_858236252_3x3-100740902-large.jpg")
        img_right = img_right.resize((950, 700), Image.LANCZOS)
        self.photo_right = ImageTk.PhotoImage(img_right)
        f_lbl_right = Label(self.root, image=self.photo_right)
        f_lbl_right.place(x=650, y=55, width=950, height=700)

        Button(self.root, text="FACE RECOGNITION", command=self.face_recog, cursor="hand2",
               font=("times new roman", 15, "bold"), bg="darkgreen", fg="white").place(x=1012, y=680, width=220, height=41)
    def mark_attendance(self, n, r, d, id):
        with open("attendance.csv", "a+", newline="\n") as f:
            f.seek(0)
            myDataList = f.readlines()
            now = datetime.now()
            today_date = now.strftime("%d/%m/%Y")

            already_marked = False
            for line in myDataList:
                if line.strip():
                    entry = line.strip().split(",")
                    if len(entry) >= 6 and entry[3] == str(id) and entry[5] == today_date:
                        already_marked = True
                        break

            if not already_marked:
                dtString = now.strftime("%H:%M:%S")
                f.write(f"{n},{r},{d},{id},{dtString},{today_date},Present\n")

    def face_recog(self):
        response = msgbox.askyesno("Face Recognition", "Start face recognition system?\n\nInstructions:\n- Look directly at the camera\n- Ensure good lighting\n- Press ESC to stop", parent=self.root)
        if not response:
            return

        student_cache = {}
        attendance_marked = {}

        def draw_boundary(img, classifier, scaleFactor, minNeighbors, clf):
            nonlocal student_cache, attendance_marked
            gray_image = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
            features = classifier.detectMultiScale(gray_image, scaleFactor, minNeighbors)

            for (x, y, w, h) in features:
                id, predict = clf.predict(gray_image[y:y+h, x:x+w])
                confidence = int(100 * (1 - predict / 300))

                # Initialize default values
                n = "Unknown"
                r = "Unknown"
                d = "Unknown"
                is_known_student = False

                if confidence > 75:
                    if id not in student_cache:
                        try:
                            conn = mysql.connector.connect(host="localhost", username="root", password="Admin@1402", database="face_recognizer")
                            my_cursor = conn.cursor()
                            my_cursor.execute("SELECT Name, Roll, Dep FROM student WHERE student_id = %s", (id,))
                            result = my_cursor.fetchone()

                            if result and result[0] and result[0].strip():
                                student_cache[id] = {
                                    'name': result[0].strip(),
                                    'roll': result[1].strip() if result[1] else "Unknown",
                                    'dept': result[2].strip() if result[2] else "Unknown",
                                    'is_valid': True
                                }
                            else:
                                student_cache[id] = {'is_valid': False}
                            conn.close()
                        except Exception as e:
                            print(f"Database error: {e}")
                            student_cache[id] = {'is_valid': False}

                    if id in student_cache and student_cache[id].get('is_valid', False):
                        n = student_cache[id]['name']
                        r = student_cache[id]['roll']
                        d = student_cache[id]['dept']
                        is_known_student = True

                if confidence > 75 and is_known_student:
                    cv2.rectangle(img, (x, y), (x+w, y+h), (0, 255, 0), 3)
                    cv2.putText(img, f"Name: {n}", (x, y-75), cv2.FONT_HERSHEY_COMPLEX, 0.7, (0, 255, 0), 2)
                    cv2.putText(img, f"Roll: {r}", (x, y-55), cv2.FONT_HERSHEY_COMPLEX, 0.7, (0, 255, 0), 2)
                    cv2.putText(img, f"Dept: {d}", (x, y-35), cv2.FONT_HERSHEY_COMPLEX, 0.7, (0, 255, 0), 2)
                    cv2.putText(img, f"ID: {id}", (x, y-15), cv2.FONT_HERSHEY_COMPLEX, 0.7, (0, 255, 0), 2)
                    cv2.putText(img, f"{confidence}%", (x, y+h+20), cv2.FONT_HERSHEY_COMPLEX, 0.6, (0, 255, 0), 2)

                    if id not in attendance_marked:
                        self.mark_attendance(n, r, d, id)
                        attendance_marked[id] = True
                elif confidence > 50:
                    cv2.rectangle(img, (x, y), (x+w, y+h), (0, 255, 255), 3)
                    cv2.putText(img, f"Unrecognized ({confidence}%)", (x, y-10), cv2.FONT_HERSHEY_COMPLEX, 0.6, (0, 255, 255), 2)
                else:
                    cv2.rectangle(img, (x, y), (x+w, y+h), (0, 0, 255), 3)
                    cv2.putText(img, f"Unknown ({confidence}%)", (x, y-10), cv2.FONT_HERSHEY_COMPLEX, 0.6, (0, 0, 255), 2)
        


        try:
            msgbox.showinfo("Loading", "Loading face recognition models...", parent=self.root)

            faceCascade = cv2.CascadeClassifier("haarcascade_frontalface_default.xml")
            if faceCascade.empty():
                msgbox.showerror("Error", "Face cascade file not found!", parent=self.root)
                return

            try:
                clf = cv2.face.LBPHFaceRecognizer_create()
                clf.read("classifier.xml")
            except:
                msgbox.showerror("Error", "Trained model not found!", parent=self.root)
                return

            msgbox.showinfo("Camera", "Initializing camera...", parent=self.root)

            video_capture = None
            for camera_index in [0, 1, 2]:
                video_capture = cv2.VideoCapture(camera_index)
                if video_capture.isOpened():
                    break
                video_capture.release()

            if not video_capture or not video_capture.isOpened():
                msgbox.showerror("Error", "Cannot open camera!", parent=self.root)
                return

            msgbox.showinfo("Ready", "Face recognition started!\n\n• Look directly at camera\n• Press ESC to stop", parent=self.root)

            while True:
                ret, img = video_capture.read()
                if not ret:
                    break
                draw_boundary(img, faceCascade, 1.1, 5, clf)
                cv2.imshow("Face Recognition", img)
                if cv2.waitKey(1) & 0xFF == 27:
                    break

            video_capture.release()
            cv2.destroyAllWindows()

            total_marked = len(attendance_marked)
            if total_marked > 0:
                msgbox.showinfo("Complete", f"Recognition stopped.\n{total_marked} student(s) marked present.", parent=self.root)
            else:
                msgbox.showinfo("Complete", "Recognition stopped.\nNo attendance marked.", parent=self.root)

        except Exception as e:
            msgbox.showerror("Error", f"Face recognition failed: {str(e)}", parent=self.root)
       
if __name__ == "__main__":
    root=Tk()
    obj=Face_Recognition(root)
    root.mainloop()          