#!/usr/bin/env python3
"""
Simple camera test script to debug camera issues
Run this script to check if your camera works with OpenCV
"""

import cv2
import sys

def test_camera():
    print("=== Camera Test Script ===")
    print("Testing camera access with OpenCV...")
    print()
    
    # Test multiple camera indices
    working_cameras = []
    
    for camera_index in range(5):
        print(f"Testing camera index {camera_index}...")
        
        try:
            # Try to open camera
            cap = cv2.VideoCapture(camera_index)
            
            if cap.isOpened():
                print(f"  ✓ Camera {camera_index} opened successfully")
                
                # Try to read a frame
                ret, frame = cap.read()
                if ret and frame is not None:
                    height, width = frame.shape[:2]
                    print(f"  ✓ Frame captured: {width}x{height}")
                    working_cameras.append(camera_index)
                    
                    # Show camera feed for 3 seconds
                    print(f"  ✓ Showing camera {camera_index} feed for 3 seconds...")
                    start_time = cv2.getTickCount()
                    
                    while True:
                        ret, frame = cap.read()
                        if not ret:
                            print("  ✗ Failed to read frame")
                            break
                            
                        cv2.imshow(f'Camera {camera_index} Test', frame)
                        
                        # Show for 3 seconds or until ESC is pressed
                        elapsed = (cv2.getTickCount() - start_time) / cv2.getTickFrequency()
                        if elapsed > 3 or cv2.waitKey(1) & 0xFF == 27:
                            break
                    
                    cv2.destroyAllWindows()
                    print(f"  ✓ Camera {camera_index} test completed")
                    
                else:
                    print(f"  ✗ Camera {camera_index} opened but cannot read frames")
                    
            else:
                print(f"  ✗ Camera {camera_index} failed to open")
                
            cap.release()
            
        except Exception as e:
            print(f"  ✗ Error with camera {camera_index}: {e}")
        
        print()
    
    # Summary
    print("=== Test Results ===")
    if working_cameras:
        print(f"✓ Working cameras found: {working_cameras}")
        print("Your camera should work with the face recognition system!")
        print(f"Recommended camera index: {working_cameras[0]}")
    else:
        print("✗ No working cameras found!")
        print("\nTroubleshooting steps:")
        print("1. Check if camera is physically connected")
        print("2. Close other applications using the camera (Skype, Teams, etc.)")
        print("3. Check Windows Device Manager for camera issues")
        print("4. Try running this script as administrator")
        print("5. Update camera drivers")
        print("6. Check Windows camera privacy settings")
    
    print("\nPress Enter to exit...")
    input()

if __name__ == "__main__":
    test_camera()
